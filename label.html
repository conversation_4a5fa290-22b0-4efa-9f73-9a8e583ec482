<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <link href="https://fonts.googleapis.com/css2?family=Figtree:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <title>Element Annotator</title>

  <script src="label1.js"></script>
  <link rel="stylesheet" href="label.css">
</head>

<body style="display: flex; flex-direction: row; align-items: stretch;">
  <!-- Left Component Menu (Accordion) -->
  <div id="leftComponentMenu" style="display: none; width: 320px; height: 100%; background: white; box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15); z-index: 3001; flex-direction: column; border-right: 1px solid #e0e0e0; display: flex; flex-direction: column;">
    <div class="menu-header ui-components-header font-figtree">
      <div class="menu-header--iconandtext">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M13.6666 4.85185L7.99998 8M7.99998 8L2.33331 4.85185M7.99998 8L8 14.3333M14 10.7057V5.29431C14 5.06588 14 4.95167 13.9663 4.8498C13.9366 4.75969 13.8879 4.67696 13.8236 4.60717C13.7509 4.52828 13.651 4.47281 13.4514 4.36188L8.51802 1.62114C8.32895 1.5161 8.23442 1.46358 8.1343 1.44299C8.0457 1.42477 7.95431 1.42477 7.8657 1.44299C7.76559 1.46358 7.67105 1.5161 7.48198 1.62114L2.54865 4.36188C2.34896 4.47281 2.24912 4.52828 2.17642 4.60717C2.11211 4.67697 2.06343 4.75969 2.03366 4.84981C2 4.95167 2 5.06588 2 5.29431V10.7057C2 10.9341 2 11.0484 2.03366 11.1502C2.06343 11.2403 2.11211 11.3231 2.17642 11.3929C2.24912 11.4718 2.34897 11.5272 2.54865 11.6382L7.48198 14.3789C7.67105 14.4839 7.76559 14.5365 7.8657 14.557C7.95431 14.5753 8.0457 14.5753 8.1343 14.557C8.23442 14.5365 8.32895 14.4839 8.51802 14.3789L13.4514 11.6382C13.651 11.5272 13.7509 11.4718 13.8236 11.3929C13.8879 11.3231 13.9366 11.2403 13.9663 11.1502C14 11.0484 14 10.9341 14 10.7057Z" stroke="#62748E" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span>Labelled components</span>
      </div>
    </div>

    <!-- Left Menu Accordion Component List -->
    <div id="leftAccordionComponentList" class="accordion-component-list" style="flex: 1; overflow-y: auto;">
      <!-- Will be populated dynamically -->
    </div>

    <!-- Original Component List (hidden by default) -->
    <div class="component-list" id="leftOriginalComponentList" style="display: none;">
      <!-- This is just a placeholder - we won't use this in the left menu -->
    </div>
  </div>

  <div id="pan-container"
    style="width: 100%; height: 100%; overflow: hidden; flex: 1; position: relative;">
    <div id="transform-container" style="position: absolute; top: 0; left: 0; right: 0; width: 100%; transform-origin: top left;">
      <div id="wrapper" style="position: relative; ">
        <img id="screenshot" src="" alt="screenshot" />
      </div>
    </div>
  </div>
  
  <!-- Right Component Selection Menu -->
  <div id="componentMenu">
    <div class="searchInput-wrapper">
      <input type="text" id="searchInput" placeholder="Search by URL or website name..." />

    </div>
    <div id="currentLabelsSection">
      <div class="menu-header">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M6 7.33333L8 9.33333L14.6667 2.66667M10.6667 2H5.2C4.0799 2 3.51984 2 3.09202 2.21799C2.71569 2.40973 2.40973 2.71569 2.21799 3.09202C2 3.51984 2 4.07989 2 5.2V10.8C2 11.9201 2 12.4802 2.21799 12.908C2.40973 13.2843 2.71569 13.5903 3.09202 13.782C3.51984 14 4.07989 14 5.2 14H10.8C11.9201 14 12.4802 14 12.908 13.782C13.2843 13.5903 13.5903 13.2843 13.782 12.908C14 12.4802 14 11.9201 14 10.8V8" stroke="#008236" stroke-width="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        <span>Labelled</span>
      </div>
      <div id="currentLabelsList" class="current-labels-list">
        <!-- Current labels will be populated here -->
      </div>
    </div>
    <div class="button-container" style="padding-bottom: 16px;">
      <!-- Save button for multi-select -->

      <div class="help-section">
        <p>If there's any confusion while labelling the bounding boxes, we encourage you to use these options</p>
        <div class="help-buttons">
          <button class="unsure-btn" id="markAsUnsure">
            <span>🤔</span>
            <span>I'm not sure</span>
          </button>
          <button class="not-ui-btn" id="markAsNotUI">
            <span>🙅🏻</span>
            <span>Not a UI element</span>
          </button>
        </div>
      </div>
      <div class="menu-header ui-components-header font-figtree">
        <div class="menu-header--iconandtext">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.6666 4.85185L7.99998 8M7.99998 8L2.33331 4.85185M7.99998 8L8 14.3333M14 10.7057V5.29431C14 5.06588 14 4.95167 13.9663 4.8498C13.9366 4.75969 13.8879 4.67696 13.8236 4.60717C13.7509 4.52828 13.651 4.47281 13.4514 4.36188L8.51802 1.62114C8.32895 1.5161 8.23442 1.46358 8.1343 1.44299C8.0457 1.42477 7.95431 1.42477 7.8657 1.44299C7.76559 1.46358 7.67105 1.5161 7.48198 1.62114L2.54865 4.36188C2.34896 4.47281 2.24912 4.52828 2.17642 4.60717C2.11211 4.67697 2.06343 4.75969 2.03366 4.84981C2 4.95167 2 5.06588 2 5.29431V10.7057C2 10.9341 2 11.0484 2.03366 11.1502C2.06343 11.2403 2.11211 11.3231 2.17642 11.3929C2.24912 11.4718 2.34897 11.5272 2.54865 11.6382L7.48198 14.3789C7.67105 14.4839 7.76559 14.5365 7.8657 14.557C7.95431 14.5753 8.0457 14.5753 8.1343 14.557C8.23442 14.5365 8.32895 14.4839 8.51802 14.3789L13.4514 11.6382C13.651 11.5272 13.7509 11.4718 13.8236 11.3929C13.8879 11.3231 13.9366 11.2403 13.9663 11.1502C14 11.0484 14 10.9341 14 10.7057Z" stroke="#62748E" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span>UI Components</span>
        </div>

        <button class="save-btn" id="saveLabelsBtn">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.66667 2V4.26667C4.66667 4.64004 4.66667 4.82672 4.73933 4.96933C4.80324 5.09477 4.90523 5.19676 5.03067 5.26067C5.17328 5.33333 5.35997 5.33333 5.73333 5.33333H10.2667C10.64 5.33333 10.8267 5.33333 10.9693 5.26067C11.0948 5.19676 11.1968 5.09477 11.2607 4.96933C11.3333 4.82672 11.3333 4.64004 11.3333 4.26667V2.66667M11.3333 14V9.73333C11.3333 9.35997 11.3333 9.17328 11.2607 9.03067C11.1968 8.90523 11.0948 8.80324 10.9693 8.73933C10.8267 8.66667 10.64 8.66667 10.2667 8.66667H5.73333C5.35997 8.66667 5.17328 8.66667 5.03067 8.73933C4.90523 8.80324 4.80324 8.90523 4.73933 9.03067C4.66667 9.17328 4.66667 9.35997 4.66667 9.73333V14M14 6.21699V10.8C14 11.9201 14 12.4802 13.782 12.908C13.5903 13.2843 13.2843 13.5903 12.908 13.782C12.4802 14 11.9201 14 10.8 14H5.2C4.0799 14 3.51984 14 3.09202 13.782C2.71569 13.5903 2.40973 13.2843 2.21799 12.908C2 12.4802 2 11.9201 2 10.8V5.2C2 4.0799 2 3.51984 2.21799 3.09202C2.40973 2.71569 2.71569 2.40973 3.09202 2.21799C3.51984 2 4.0799 2 5.2 2H9.78301C10.1091 2 10.2722 2 10.4256 2.03684C10.5617 2.0695 10.6918 2.12337 10.811 2.19648C10.9456 2.27894 11.0609 2.39424 11.2915 2.62484L13.3752 4.7085C13.6058 4.9391 13.7211 5.0544 13.8035 5.18895C13.8766 5.30825 13.9305 5.43831 13.9632 5.57436C14 5.72781 14 5.89087 14 6.21699Z" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span>Save labels</span>
        </button>
      </div>

      <!-- Accordion Component List -->
      <div id="accordionComponentList" class="accordion-component-list" style="display: none;">
        <!-- Will be populated dynamically -->
      </div>

      <!-- Original Component List -->
      <div class="component-list" id="originalComponentList">
        <div class="component-item">
          <input type="checkbox" id="logo-checkbox" data-type="Logo">
          <label for="logo-checkbox">Logo</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="brand-logo-checkbox" data-type="Brand Logo">
          <label for="brand-logo-checkbox">Brand Logo</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="customer-logo-checkbox" data-type="Customer Logo">
          <label for="customer-logo-checkbox">Customer Logo</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="footer-checkbox" data-type="Footer">
          <label for="footer-checkbox">Footer</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="rating-checkbox" data-type="Rating">
          <label for="rating-checkbox">Rating</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="slider-checkbox" data-type="Slider">
          <label for="slider-checkbox">Slider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="continuous-slider-checkbox" data-type="Continuous Slider - Slider">
          <label for="continuous-slider-checkbox">Continuous Slider - Slider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="discrete-slider-checkbox" data-type="Discrete Slider - Slider">
          <label for="discrete-slider-checkbox">Discrete Slider - Slider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="range-slider-checkbox" data-type="Range Slider - Slider">
          <label for="range-slider-checkbox">Range Slider - Slider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="disabled-slider-checkbox" data-type="Disabled Slider - Slider">
          <label for="disabled-slider-checkbox">Disabled Slider - Slider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="testimonial-checkbox" data-type="Testimonial">
          <label for="testimonial-checkbox">Testimonial</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="table-checkbox" data-type="Table">
          <label for="table-checkbox">Table</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="pricing-table-checkbox" data-type="Pricing Table">
          <label for="pricing-table-checkbox">Pricing Table</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="social-share-checkbox" data-type="Social share">
          <label for="social-share-checkbox">Social share</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="cookie-banner-checkbox" data-type="Cookie banner">
          <label for="cookie-banner-checkbox">Cookie banner</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="file-upload-checkbox" data-type="File upload">
          <label for="file-upload-checkbox">File upload</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="maps-checkbox" data-type="maps">
          <label for="maps-checkbox">maps</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="heading-checkbox" data-type="Heading">
          <label for="heading-checkbox">Heading</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="page-heading-checkbox" data-type="Page Heading - Heading">
          <label for="page-heading-checkbox">Page Heading - Heading</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="section-heading-checkbox" data-type="Section Heading - Heading">
          <label for="section-heading-checkbox">Section Heading - Heading</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="sub-heading-checkbox" data-type="Sub Heading - Heading">
          <label for="sub-heading-checkbox">Sub Heading - Heading</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="card-heading-checkbox" data-type="Card Heading - Heading">
          <label for="card-heading-checkbox">Card Heading - Heading</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="accordion-heading-checkbox" data-type="Accordion Heading - Heading">
          <label for="accordion-heading-checkbox">Accordion Heading - Heading</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="paragraph-checkbox" data-type="Paragraph">
          <label for="paragraph-checkbox">Paragraph</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="image-checkbox" data-type="Image">
          <label for="image-checkbox">Image</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="image-with-caption-checkbox" data-type="Image with caption">
          <label for="image-with-caption-checkbox">Image with caption</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="lightbox-gallery-checkbox" data-type="Lightbox gallery">
          <label for="lightbox-gallery-checkbox">Lightbox gallery</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="standard-image-checkbox" data-type="Standard Image - Image">
          <label for="standard-image-checkbox">Standard Image - Image</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="interactive-image-checkbox" data-type="Interactive Image - Image">
          <label for="interactive-image-checkbox">Interactive Image - Image</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="thumbnail-image-checkbox" data-type="Thumbnail Image - Image">
          <label for="thumbnail-image-checkbox">Thumbnail Image - Image</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="text-inputs-checkbox" data-type="Text Inputs">
          <label for="text-inputs-checkbox">Text Inputs</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="single-line-input-checkbox" data-type="Single-line input">
          <label for="single-line-input-checkbox">Single-line input</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="password-input-checkbox" data-type="Password input">
          <label for="password-input-checkbox">Password input</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="search-bar-input-checkbox" data-type="Search bar input">
          <label for="search-bar-input-checkbox">Search bar input</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="text-areas-checkbox" data-type="Text Areas - Text Input">
          <label for="text-areas-checkbox">Text Areas - Text Input</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="text-areas-with-counter-checkbox"
            data-type="Text Areas with character counter - Text Input">
          <label for="text-areas-with-counter-checkbox">Text Areas with character counter - Text Input</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="slider-input-checkbox" data-type="Slider - Input">
          <label for="slider-input-checkbox">Slider - Input</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="checkboxes-checkbox" data-type="Checkboxes">
          <label for="checkboxes-checkbox">Checkboxes</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="basic-checkbox-checkbox" data-type="Basic checkbox">
          <label for="basic-checkbox-checkbox">Basic checkbox</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="checkbox-with-label-checkbox" data-type="Checkbox with label and description">
          <label for="checkbox-with-label-checkbox">Checkbox with label and description</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="checkbox-group-checkbox" data-type="Checkbox group (multi-select)">
          <label for="checkbox-group-checkbox">Checkbox group (multi-select)</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="tri-state-checkbox-checkbox" data-type="Tri-state Checkbox - Checkbox">
          <label for="tri-state-checkbox-checkbox">Tri-state Checkbox - Checkbox</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="disabled-checkbox-checkbox" data-type="Disabled Checkbox - Checkbox">
          <label for="disabled-checkbox-checkbox">Disabled Checkbox - Checkbox</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="radio-buttons-checkbox" data-type="Radio Buttons">
          <label for="radio-buttons-checkbox">Radio Buttons</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="basic-radio-group-checkbox" data-type="Basic radio group">
          <label for="basic-radio-group-checkbox">Basic radio group</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="inline-radio-group-checkbox" data-type="Inline radio group">
          <label for="inline-radio-group-checkbox">Inline radio group</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="disabled-radio-button-checkbox" data-type="Disabled Radio Button - Radio Button">
          <label for="disabled-radio-button-checkbox">Disabled Radio Button - Radio Button</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="toggles-switches-checkbox" data-type="Toggles & Switches">
          <label for="toggles-switches-checkbox">Toggles & Switches</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="basic-toggle-checkbox" data-type="Basic toggle">
          <label for="basic-toggle-checkbox">Basic toggle</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="labeled-toggle-checkbox" data-type="Labeled toggle">
          <label for="labeled-toggle-checkbox">Labeled toggle</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="on-off-toggle-checkbox" data-type="On/Off Toggle - Toggle Switch">
          <label for="on-off-toggle-checkbox">On/Off Toggle - Toggle Switch</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="disabled-toggle-checkbox" data-type="Disabled Toggle - Toggle Switch">
          <label for="disabled-toggle-checkbox">Disabled Toggle - Toggle Switch</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="dropdown-checkbox" data-type="Dropdown">
          <label for="dropdown-checkbox">Dropdown</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="multi-select-dropdown-checkbox" data-type="Multi-select Dropdown - Dropdown">
          <label for="multi-select-dropdown-checkbox">Multi-select Dropdown - Dropdown</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="searchable-dropdown-checkbox" data-type="Searchable Dropdown - Dropdown">
          <label for="searchable-dropdown-checkbox">Searchable Dropdown - Dropdown</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="disabled-dropdown-checkbox" data-type="Disabled Dropdown - Dropdown">
          <label for="disabled-dropdown-checkbox">Disabled Dropdown - Dropdown</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="navigation-bars-checkbox" data-type="Navigation Bars">
          <label for="navigation-bars-checkbox">Navigation Bars</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="top-navigation-bar-checkbox" data-type="Top Navigation Bar - Navigation Bar">
          <label for="top-navigation-bar-checkbox">Top Navigation Bar - Navigation Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="sidebar-navigation-checkbox" data-type="Sidebar Navigation - Navigation Bar">
          <label for="sidebar-navigation-checkbox">Sidebar Navigation - Navigation Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="bottom-navigation-checkbox" data-type="Bottom Navigation - Navigation Bar">
          <label for="bottom-navigation-checkbox">Bottom Navigation - Navigation Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="breadcrumbs-checkbox" data-type="Breadcrumbs">
          <label for="breadcrumbs-checkbox">Breadcrumbs</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="basic-breadcrumb-trail-checkbox" data-type="Basic breadcrumb trail">
          <label for="basic-breadcrumb-trail-checkbox">Basic breadcrumb trail</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="with-icons-checkbox" data-type="With icons">
          <label for="with-icons-checkbox">With icons</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="collapsed-breadcrumb-checkbox" data-type="Collapsed breadcrumb for long paths">
          <label for="collapsed-breadcrumb-checkbox">Collapsed breadcrumb for long paths</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="tabs-checkbox" data-type="Tabs">
          <label for="tabs-checkbox">Tabs</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="basic-horizontal-tabs-checkbox" data-type="Basic horizontal tabs">
          <label for="basic-horizontal-tabs-checkbox">Basic horizontal tabs</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="icon-tabs-checkbox" data-type="Icon tabs">
          <label for="icon-tabs-checkbox">Icon tabs</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="standard-tab-checkbox" data-type="Standard Tab - Tab Bar">
          <label for="standard-tab-checkbox">Standard Tab - Tab Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="scrollable-tab-bar-checkbox" data-type="Scrollable Tab Bar - Tab Bar">
          <label for="scrollable-tab-bar-checkbox">Scrollable Tab Bar - Tab Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="icon-tab-checkbox" data-type="Icon Tab - Tab Bar">
          <label for="icon-tab-checkbox">Icon Tab - Tab Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="pagination-checkbox" data-type="Pagination">
          <label for="pagination-checkbox">Pagination</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="numbered-pagination-checkbox" data-type="Numbered pagination">
          <label for="numbered-pagination-checkbox">Numbered pagination</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="prev-next-style-checkbox" data-type="Prev/Next style">
          <label for="prev-next-style-checkbox">Prev/Next style</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="with-page-jumper-checkbox" data-type="With page jumper">
          <label for="with-page-jumper-checkbox">With page jumper</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="modals-checkbox" data-type="Modals">
          <label for="modals-checkbox">Modals</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="centered-modal-checkbox" data-type="Centered modal">
          <label for="centered-modal-checkbox">Centered modal</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="full-screen-modal-checkbox" data-type="Full-screen modal">
          <label for="full-screen-modal-checkbox">Full-screen modal</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="tooltips-checkbox" data-type="Tooltips">
          <label for="tooltips-checkbox">Tooltips</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="standard-tooltip-checkbox" data-type="Standard Tooltip - Tooltip">
          <label for="standard-tooltip-checkbox">Standard Tooltip - Tooltip</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="tooltip-with-cta-checkbox" data-type="Tooltip with CTA - Tooltip">
          <label for="tooltip-with-cta-checkbox">Tooltip with CTA - Tooltip</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="toasts-checkbox" data-type="Toasts">
          <label for="toasts-checkbox">Toasts</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="success-message-checkbox" data-type="Success message">
          <label for="success-message-checkbox">Success message</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="info-message-with-action-checkbox" data-type="Info message with action button">
          <label for="info-message-with-action-checkbox">Info message with action button</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="alerts-checkbox" data-type="Alerts">
          <label for="alerts-checkbox">Alerts</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="inline-error-message-checkbox" data-type="Inline error message">
          <label for="inline-error-message-checkbox">Inline error message</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="warning-banner-checkbox" data-type="Warning banner">
          <label for="warning-banner-checkbox">Warning banner</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="confirm-alert-box-checkbox" data-type="Confirm alert box">
          <label for="confirm-alert-box-checkbox">Confirm alert box</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="tables-checkbox" data-type="Tables">
          <label for="tables-checkbox">Tables</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="basic-table-checkbox" data-type="Basic table">
          <label for="basic-table-checkbox">Basic table</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="table-with-sorting-filtering-checkbox" data-type="Table with sorting-filtering">
          <label for="table-with-sorting-filtering-checkbox">Table with sorting-filtering</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="table-with-row-actions-checkbox" data-type="Table with row actions">
          <label for="table-with-row-actions-checkbox">Table with row actions</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="card-checkbox" data-type="Card">
          <label for="card-checkbox">Card</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="standard-card-checkbox" data-type="Standard Card - Card">
          <label for="standard-card-checkbox">Standard Card - Card</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="image-card-checkbox" data-type="Image Card - Card">
          <label for="image-card-checkbox">Image Card - Card</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="product-pricing-card-checkbox" data-type="Product or pricing card">
          <label for="product-pricing-card-checkbox">Product or pricing card</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="lists-checkbox" data-type="Lists">
          <label for="lists-checkbox">Lists</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="bulleted-list-checkbox" data-type="Bulleted list">
          <label for="bulleted-list-checkbox">Bulleted list</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="list-with-avatars-checkbox" data-type="List with avatars">
          <label for="list-with-avatars-checkbox">List with avatars</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="interactive-list-items-checkbox" data-type="Interactive list items">
          <label for="interactive-list-items-checkbox">Interactive list items</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="ordered-list-checkbox" data-type="Ordered List - List">
          <label for="ordered-list-checkbox">Ordered List - List</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="unordered-list-checkbox" data-type="Unordered List - List">
          <label for="unordered-list-checkbox">Unordered List - List</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="badges-checkbox" data-type="Badges">
          <label for="badges-checkbox">Badges</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="status-badge-checkbox" data-type="Status badge">
          <label for="status-badge-checkbox">Status badge</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="number-counter-badge-checkbox" data-type="Number or Counter badge">
          <label for="number-counter-badge-checkbox">Number or Counter badge</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="icon-badge-checkbox" data-type="Icon badge">
          <label for="icon-badge-checkbox">Icon badge</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="buttons-checkbox" data-type="Buttons">
          <label for="buttons-checkbox">Buttons</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="primary-button-checkbox" data-type="Primary Button - Button">
          <label for="primary-button-checkbox">Primary Button - Button</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="secondary-button-checkbox" data-type="Secondary Button - Button">
          <label for="secondary-button-checkbox">Secondary Button - Button</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="icon-button-checkbox" data-type="Icon button - Button">
          <label for="icon-button-checkbox">Icon button - Button</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="ghost-button-checkbox" data-type="Ghost Button - Button">
          <label for="ghost-button-checkbox">Ghost Button - Button</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="disabled-button-checkbox" data-type="Disabled Button - Button">
          <label for="disabled-button-checkbox">Disabled Button - Button</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="basic-form-checkbox" data-type="Basic Form">
          <label for="basic-form-checkbox">Basic Form</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="name-email-message-checkbox" data-type="Name email message">
          <label for="name-email-message-checkbox">Name email message</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="login-form-checkbox" data-type="Login form">
          <label for="login-form-checkbox">Login form</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="signup-form-checkbox" data-type="Signup form">
          <label for="signup-form-checkbox">Signup form</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="advanced-layouts-checkbox" data-type="Advanced Layouts">
          <label for="advanced-layouts-checkbox">Advanced Layouts</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="multi-step-form-checkbox" data-type="Multi-step form">
          <label for="multi-step-form-checkbox">Multi-step form</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="avatars-checkbox" data-type="Avatars">
          <label for="avatars-checkbox">Avatars</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="initials-avatar-checkbox" data-type="Initials avatar">
          <label for="initials-avatar-checkbox">Initials avatar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="image-avatar-checkbox" data-type="Image avatar">
          <label for="image-avatar-checkbox">Image avatar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="avatar-with-status-dot-checkbox" data-type="Avatar with status dot">
          <label for="avatar-with-status-dot-checkbox">Avatar with status dot</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="user-avatar-checkbox" data-type="User Avatar - Avatar">
          <label for="user-avatar-checkbox">User Avatar - Avatar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="group-avatar-checkbox" data-type="Group Avatar - Avatar">
          <label for="group-avatar-checkbox">Group Avatar - Avatar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="video-embeds-checkbox" data-type="Video Embeds">
          <label for="video-embeds-checkbox">Video Embeds</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="embedded-player-checkbox" data-type="Embedded player">
          <label for="embedded-player-checkbox">Embedded player</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="video-with-controls-checkbox" data-type="Video with controls">
          <label for="video-with-controls-checkbox">Video with controls</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="spinners-checkbox" data-type="Spinners">
          <label for="spinners-checkbox">Spinners</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="circular-spinner-checkbox" data-type="Circular spinner">
          <label for="circular-spinner-checkbox">Circular spinner</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="dots-animation-checkbox" data-type="Dots animation">
          <label for="dots-animation-checkbox">Dots animation</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="linear-progress-bar-checkbox" data-type="Linear progress bar">
          <label for="linear-progress-bar-checkbox">Linear progress bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="skeleton-screens-checkbox" data-type="Skeleton Screens">
          <label for="skeleton-screens-checkbox">Skeleton Screens</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="text-image-skeleton-checkbox" data-type="Text and image skeleton">
          <label for="text-image-skeleton-checkbox">Text and image skeleton</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="card-skeleton-checkbox" data-type="Card skeleton">
          <label for="card-skeleton-checkbox">Card skeleton</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="table-skeleton-checkbox" data-type="Table skeleton">
          <label for="table-skeleton-checkbox">Table skeleton</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="side-drawers-checkbox" data-type="Side Drawers">
          <label for="side-drawers-checkbox">Side Drawers</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="left-side-drawer-checkbox" data-type="Left-side drawer">
          <label for="left-side-drawer-checkbox">Left-side drawer</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="right-side-with-settings-checkbox" data-type="Right-side with settings">
          <label for="right-side-with-settings-checkbox">Right-side with settings</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="chips-checkbox" data-type="Chips">
          <label for="chips-checkbox">Chips</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="filter-chips-checkbox" data-type="Filter chips">
          <label for="filter-chips-checkbox">Filter chips</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="choice-chips-checkbox" data-type="Choice chips">
          <label for="choice-chips-checkbox">Choice chips</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="accordions-checkbox" data-type="Accordions">
          <label for="accordions-checkbox">Accordions</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="basic-accordion-checkbox" data-type="Basic accordion">
          <label for="basic-accordion-checkbox">Basic accordion</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="nested-accordion-checkbox" data-type="Nested accordion">
          <label for="nested-accordion-checkbox">Nested accordion</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="steppers-checkbox" data-type="Steppers">
          <label for="steppers-checkbox">Steppers</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="horizontal-stepper-checkbox" data-type="Horizontal stepper">
          <label for="horizontal-stepper-checkbox">Horizontal stepper</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="vertical-wizard-checkbox" data-type="Vertical wizard">
          <label for="vertical-wizard-checkbox">Vertical wizard</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="calendars-pickers-checkbox" data-type="Calendars & Pickers">
          <label for="calendars-pickers-checkbox">Calendars & Pickers</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="date-picker-checkbox" data-type="Date picker">
          <label for="date-picker-checkbox">Date picker</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="time-picker-checkbox" data-type="Time picker">
          <label for="time-picker-checkbox">Time picker</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="date-range-picker-checkbox" data-type="Date range picker">
          <label for="date-range-picker-checkbox">Date range picker</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="progress-bar-checkbox" data-type="Progress Bar">
          <label for="progress-bar-checkbox">Progress Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="determinate-progress-bar-checkbox"
            data-type="Determinate Progress Bar - Progress Bar">
          <label for="determinate-progress-bar-checkbox">Determinate Progress Bar - Progress Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="indeterminate-progress-bar-checkbox"
            data-type="Indeterminate Progress Bar - Progress Bar">
          <label for="indeterminate-progress-bar-checkbox">Indeterminate Progress Bar - Progress Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="segmented-progress-bar-checkbox" data-type="Segmented Progress Bar - Progress Bar">
          <label for="segmented-progress-bar-checkbox">Segmented Progress Bar - Progress Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="search-bar-checkbox" data-type="Search Bar">
          <label for="search-bar-checkbox">Search Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="standard-search-bar-checkbox" data-type="Standard Search Bar - Search Bar">
          <label for="standard-search-bar-checkbox">Standard Search Bar - Search Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="autocomplete-search-bar-checkbox" data-type="Autocomplete Search Bar - Search Bar">
          <label for="autocomplete-search-bar-checkbox">Autocomplete Search Bar - Search Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="voice-search-bar-checkbox" data-type="Voice Search Bar - Search Bar">
          <label for="voice-search-bar-checkbox">Voice Search Bar - Search Bar</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="form-field-checkbox" data-type="Form Field">
          <label for="form-field-checkbox">Form Field</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="standard-form-field-checkbox" data-type="Standard Form Field - Form Field">
          <label for="standard-form-field-checkbox">Standard Form Field - Form Field</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="inline-form-field-checkbox" data-type="Inline Form Field - Form Field">
          <label for="inline-form-field-checkbox">Inline Form Field - Form Field</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="grouped-form-field-checkbox" data-type="Grouped Form Field - Form Field">
          <label for="grouped-form-field-checkbox">Grouped Form Field - Form Field</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="disabled-form-field-checkbox" data-type="Disabled Form Field - Form Field">
          <label for="disabled-form-field-checkbox">Disabled Form Field - Form Field</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="menu-checkbox" data-type="Menu">
          <label for="menu-checkbox">Menu</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="dropdown-menu-checkbox" data-type="Dropdown Menu - Menu">
          <label for="dropdown-menu-checkbox">Dropdown Menu - Menu</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="context-menu-checkbox" data-type="Context Menu - Menu">
          <label for="context-menu-checkbox">Context Menu - Menu</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="hamburger-menu-checkbox" data-type="Hamburger Menu - Menu">
          <label for="hamburger-menu-checkbox">Hamburger Menu - Menu</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="loading-indicator-checkbox" data-type="Loading Indicator">
          <label for="loading-indicator-checkbox">Loading Indicator</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="spinner-loader-checkbox" data-type="Spinner Loader - Loading Indicator">
          <label for="spinner-loader-checkbox">Spinner Loader - Loading Indicator</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="progress-ring-checkbox" data-type="Progress Ring - Loading Indicator">
          <label for="progress-ring-checkbox">Progress Ring - Loading Indicator</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="skeleton-loader-checkbox" data-type="Skeleton Loader - Loading Indicator">
          <label for="skeleton-loader-checkbox">Skeleton Loader - Loading Indicator</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="icon-checkbox" data-type="Icon">
          <label for="icon-checkbox">Icon</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="standard-icon-checkbox" data-type="Standard Icon - Icon">
          <label for="standard-icon-checkbox">Standard Icon - Icon</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="interactive-icon-checkbox" data-type="Interactive Icon - Icon">
          <label for="interactive-icon-checkbox">Interactive Icon - Icon</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="status-icon-checkbox" data-type="Status Icon - Icon">
          <label for="status-icon-checkbox">Status Icon - Icon</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="chart-checkbox" data-type="Chart">
          <label for="chart-checkbox">Chart</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="bar-chart-checkbox" data-type="Bar Chart - Chart">
          <label for="bar-chart-checkbox">Bar Chart - Chart</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="line-chart-checkbox" data-type="Line Chart - Chart">
          <label for="line-chart-checkbox">Line Chart - Chart</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="pie-chart-checkbox" data-type="Pie Chart - Chart">
          <label for="pie-chart-checkbox">Pie Chart - Chart</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="scatter-plot-checkbox" data-type="Scatter Plot - Chart">
          <label for="scatter-plot-checkbox">Scatter Plot - Chart</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="divider-checkbox" data-type="Divider">
          <label for="divider-checkbox">Divider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="horizontal-divider-checkbox" data-type="Horizontal Divider - Divider">
          <label for="horizontal-divider-checkbox">Horizontal Divider - Divider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="vertical-divider-checkbox" data-type="Vertical Divider - Divider">
          <label for="vertical-divider-checkbox">Vertical Divider - Divider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="dashed-divider-checkbox" data-type="Dashed Divider - Divider">
          <label for="dashed-divider-checkbox">Dashed Divider - Divider</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="side-panel-checkbox" data-type="Side Panel">
          <label for="side-panel-checkbox">Side Panel</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="left-side-panel-checkbox" data-type="Left Side Panel - Side Panel">
          <label for="left-side-panel-checkbox">Left Side Panel - Side Panel</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="right-side-panel-checkbox" data-type="Right Side Panel - Side Panel">
          <label for="right-side-panel-checkbox">Right Side Panel - Side Panel</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="collapsible-side-panel-checkbox" data-type="Collapsible Side Panel - Side Panel">
          <label for="collapsible-side-panel-checkbox">Collapsible Side Panel - Side Panel</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="overlay-side-panel-checkbox" data-type="Overlay Side Panel - Side Panel">
          <label for="overlay-side-panel-checkbox">Overlay Side Panel - Side Panel</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="link-checkbox" data-type="Link">
          <label for="link-checkbox">Link</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="banner-checkbox" data-type="Banner">
          <label for="banner-checkbox">Banner</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="testimonial-title-checkbox" data-type="Testimonial Title">
          <label for="testimonial-title-checkbox">Testimonial Title</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="testimonial-giver-checkbox" data-type="Testimonial Giver">
          <label for="testimonial-giver-checkbox">Testimonial Giver</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="section-checkbox" data-type="Section">
          <label for="section-checkbox">Section</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="usp-title-checkbox" data-type="USP Title">
          <label for="usp-title-checkbox">USP Title</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="image-group-checkbox" data-type="Image Group">
          <label for="image-group-checkbox">Image Group</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="highlight-checkbox" data-type="Highlight">
          <label for="highlight-checkbox">Highlight</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="bullet-checkbox" data-type="Bullet">
          <label for="bullet-checkbox">Bullet</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="annotation-title-checkbox" data-type="Annotation Title">
          <label for="annotation-title-checkbox">Annotation Title</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="arrow-shape-checkbox" data-type="Arrow - Shape">
          <label for="arrow-shape-checkbox">Arrow - Shape</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="circular-gradient-shape-checkbox" data-type="Circular Gradient - Shape">
          <label for="circular-gradient-shape-checkbox">Circular Gradient - Shape</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="gradient-blob-shape-checkbox" data-type="Gradient Blob - Shape">
          <label for="gradient-blob-shape-checkbox">Gradient Blob - Shape</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="testimonial-giver-role-checkbox" data-type="Testimonial Giver Role">
          <label for="testimonial-giver-role-checkbox">Testimonial Giver Role</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="testimonial-giver-image-checkbox" data-type="Testimonial Giver Image">
          <label for="testimonial-giver-image-checkbox">Testimonial Giver Image</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="testimonial-text-checkbox" data-type="Testimonial Text">
          <label for="testimonial-text-checkbox">Testimonial Text</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="testimonial-card-checkbox" data-type="Testimonial Card - Card">
          <label for="testimonial-card-checkbox">Testimonial Card - Card</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="testimonial-giver-name-checkbox" data-type="Testimonial Giver Name">
          <label for="testimonial-giver-name-checkbox">Testimonial Giver Name</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="tab-checkbox" data-type="Tab">
          <label for="tab-checkbox">Tab</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="badge-text-checkbox" data-type="Badge Text">
          <label for="badge-text-checkbox">Badge Text</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="badge-icon-checkbox" data-type="Badge Icon">
          <label for="badge-icon-checkbox">Badge Icon</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="card-content-checkbox" data-type="Card Content - Paragraph">
          <label for="card-content-checkbox">Card Content - Paragraph</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="card-image-checkbox" data-type="Card Image - Image">
          <label for="card-image-checkbox">Card Image - Image</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="card-row-checkbox" data-type="Card Row - Row">
          <label for="card-row-checkbox">Card Row - Row</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="social-icon-checkbox" data-type="Social icon - Icon">
          <label for="social-icon-checkbox">Social icon - Icon</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="footer-section-title-checkbox" data-type="Footer Section Title - Heading">
          <label for="footer-section-title-checkbox">Footer Section Title - Heading</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="link-list-item-checkbox" data-type="Link - List item">
          <label for="link-list-item-checkbox">Link - List item</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="country-icon-checkbox" data-type="Country Icon - Icon">
          <label for="country-icon-checkbox">Country Icon - Icon</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="button-text-checkbox" data-type="Button Text">
          <label for="button-text-checkbox">Button Text</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="columns-checkbox" data-type="Columns">
          <label for="columns-checkbox">Columns</label>
        </div>
        <div class="component-item">
          <input type="checkbox" id="banner-section-checkbox" data-type="Banner - Section">
          <label for="banner-section-checkbox">Banner - Section</label>
        </div>
      </div>

    </div>
  </div>

  <div id="tooltip" class="tooltip"></div>

  <!-- Debug overlay -->
  <div id="debugOverlay"
    style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; z-index: 9999; font-size: 12px; max-width: 300px; display: none;">
    <div>Pan: <span id="debugPan">X: 0, Y: 0</span></div>
    <div>Zoom: <span id="debugZoom">100%</span></div>
    <div>Mouse down: <span id="debugMouseDown">false</span></div>
    <div>Dragging: <span id="debugDragging">false</span></div>
    <div>Mouse pos: <span id="debugMousePos">0, 0</span></div>
    <div>Last event: <span id="debugLastEvent">none</span></div>
  </div>



  <!-- Toggle Button -->
  <!-- <button id="toggleListBtn">Show Elements</button> -->

  <!-- Modal List -->
  <!-- <div id="elementListModal">
    <h4 style="margin-top: 0;">UI Elements</h4>
    <ul id="elementList"></ul>
  </div> -->

  <!-- Popup Overlay -->
  <div id="popupOverlay"></div>

  <!-- Multi-label Popup -->
  <div id="multiLabelPopup">
    <div class="popup-arrow"></div>
    <p>Do you want to apply these labels to all bounding boxes with the same selector?</p>
    <div class="popup-buttons">
      <button id="yesBtn">Yes</button>
      <button id="noBtn">No</button>
    </div>
  </div>

  <script src="label2.js"></script>

</body>

</html>