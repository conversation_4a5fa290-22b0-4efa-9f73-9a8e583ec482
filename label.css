html,
body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    /* Prevent scrollbars when panning */
    font-family: 'Figtree', sans-serif;
    will-change: transform;
    /* Optimize for animations */
    touch-action: none;
    /* Disable browser handling of touch gestures */
    height: 100%;
    width: 100%;
}

* {
    font-family: 'Figtree', sans-serif;
}

#wrapper {
    position: relative;
    display: block;
    width: 100%;
    height: auto;
    transform-origin: top left;
    will-change: transform;
    /* Optimize for animations */
    backface-visibility: hidden;
    /* Prevent flickering during animations */
    -webkit-font-smoothing: antialiased;
    /* Smoother text rendering */
    -moz-osx-font-smoothing: grayscale;
    /* Smoother text rendering for Firefox */
    cursor: grab;
    /* Show grab cursor to indicate draggable */
    z-index: 1;
    /* Ensure wrapper is above other elements */
    user-select: none;
    /* Prevent text selection during drag */
    touch-action: none;
    /* Disable browser handling of all panning and zooming gestures */
}

#screenshot {
    display: block;
    width: 100%;
    transition: transform 0.2s ease-out;
    /* Smooth transition for zoom */

    pointer-events: none;
}

.box {
    position: absolute;
    border: 1px dashed #007bff;
    /* Default box style */
    pointer-events: auto;
    background-color: rgba(0, 123, 255, 0.00);
    /* Default box background */
    transition: background-color 150ms ease-in, border 150ms ease-in, box-shadow 150ms ease-in;
}

/* Label display on boxes */
.label-tag {
    position: absolute;
    top: -22px;
    left: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: rgba(255, 255, 255, 0.4);
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    pointer-events: none;
    /* Prevent the label from blocking clicks */
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Container for multiple labels */
.labels-container {
    position: absolute;
    top: -28px;
    left: 0;
    z-index: 1000;
    display: flex;
    flex-direction: row;
    gap: 2px;

}

/* Individual label style */
.multi-label {
    background-color: rgba(0, 0, 0, 0.9);
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    border-radius: 3px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 0;
    overflow: hidden;
    /* gap: 4px; */
    /* pointer-events: none; */

    span {
        line-height: 1;
    }

    span:first-child {
        padding: 3px 2px 4px 6px;
        pointer-events: none;
    }
}

/* Cross icon for removing labels */
.label-remove {
    height: 14px;
    border-radius: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
    line-height: 1;
    vertical-align: middle;
    padding: 0;
    height: 21px;
    aspect-ratio: 1;

}

.label-remove:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Additional styles for multi-label display */

.box:hover {
    /* background-color: rgba(255, 0, 0, 0.2); */
    border-color: transparent;
    /* border-color: red; */
    cursor: pointer;
}

/* Hover effect for annotated boxes */
.annotated-box:hover {
    background-color: rgba(255, 0, 0, 0.3) !important;
    border-color: #FF0000 !important;
    cursor: pointer !important;
    opacity: 1 !important;
}

/* When highlighting is active, reduce opacity of all boxes */
.highlighting-active .box:not(.highlighted) {
    opacity: 0.1;

    /* transition: opacity 0.2s ease-out; */
    .multi-label {
        display: none;
    }
}

/* .highlighting-active-review .box {
    opacity: 0.1;
} */

.highlighted {
    border: 2px solid orange !important;
    background-color: rgba(255, 165, 0, 0.2) !important;
}

/* Ensure highlighted boxes remain fully visible */
.highlighting-active .box.highlighted {
    opacity: 1 !important;
}

/* Labeled element styles - from annotation_object.json */
.annotated-box {
    border: 3px solid #F44336 !important;
    /* Red solid border for annotated boxes */
    background-color: rgba(244, 67, 54, 0.2) !important;
    /* Light red background for annotated boxes */
    pointer-events: auto !important;
    /* Ensure pointer events work */
    cursor: pointer !important;
    /* Show pointer cursor */
}


.labeled-ui {
    border: 2px solid #4CAF50 !important;
    background-color: rgba(76, 175, 80, 0.1) !important;
}

.labeled-ui:hover {
    background-color: rgba(76, 175, 80, 0.3) !important;
    border-color: #3d8b40 !important;
    cursor: pointer;
}

.labeled-ui:hover {
    background-color: rgba(76, 175, 80, 0.3) !important;
    border-color: #3d8b40 !important;
    cursor: pointer;
}

.labeled-unsure {
    border: 2px solid #FF9800 !important;
    background-color: rgba(255, 152, 0, 0.2) !important;
}

.labeled-unsure:hover {
    background-color: rgba(255, 152, 0, 0.3) !important;
    border-color: #e68900 !important;
    cursor: pointer;
}

.labeled-not-ui {
    border: 2px solid #9E9E9E !important;
    background-color: rgba(158, 158, 158, 0.2) !important;
}

.labeled-not-ui:hover {
    background-color: rgba(158, 158, 158, 0.3) !important;
    border-color: #8e8e8e !important;
    cursor: pointer;
}

.tooltip {
    position: absolute;
    background: #fff;
    border: 1px solid #333;
    padding: 4px 8px;
    font-size: 12px;
    z-index: 1000;
    display: none !important;
    /* Force tooltip to always be hidden */
}

/* Component Selection Menu */
#componentMenu {
    /* position: fixed; */
    width: 340px;
    height: 100vh;

    background: white;
    box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
    z-index: 3001;
    /* Higher z-index to ensure it's above the popup overlay */
    display: flex;
    overflow-y: auto;
    flex-direction: column;
    border-left: 1px solid #e0e0e0;
}

/* Left Component Menu */
#leftComponentMenu {
    /* position: fixed; */
    width: 400px;
    height: 100vh;

    background: white;
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
    z-index: 3001;
    display: none;
    overflow-y: auto;
    flex-direction: column;
    border-right: 1px solid #e0e0e0;
}

/* Current Labels Section */
#currentLabelsSection {
    display: flex;
    flex-direction: column;

}

#currentLabelsSection .menu-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;

    span {
        color: var(--color-green-700);
        font-family: Figtree;
        font-size: 15px;
        font-weight: 500;
        line-height: 1;
    }

    svg {
        display: inline-block;
    }
}

.current-labels-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    padding: 12px;

    border-bottom: 1px solid var(--slate-200, #E2E8F0);



}

.current-label-item {
    background-color: #62748E;
    color: white;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 0;
    overflow: hidden;
    border: none;

    transition: all 0.1s ease-in;

    &:hover {
        background-color: hsl(215, 22%, 32%);
    }
}

.current-label-item span:first-child {
    padding: 4px 6px 4px 8px;
    pointer-events: none;
}

.current-label-remove {
    height: 20px;
    width: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.1s ease-in;
    line-height: 1;
    vertical-align: middle;
    padding: 0;
    color: white;
    opacity: 0.7;
}

.current-label-remove:hover {
    opacity: 1;
}

.searchInput-wrapper {
    display: flex;
    flex-direction: column;
    padding: 12px;
}

#searchInput {
    width: 100%;
    padding: 12px 16px;
    box-sizing: border-box;
    font-size: 14px;
    outline: none;
    border-radius: 5px;
    transition: all ease-in 200ms;
    height: 38px;
    border: 1px solid #E2E8F0;
    width: calc(100% - 24px);
    background-color: #F8FAFC;
    color: #314158;
    font-family: 'Figtree', sans-serif;
}

#searchInput::placeholder {
    color: #90A1B9;
}

#searchInput:focus,
#searchInput:focus-within {
    background-color: #ffffff;
    border-color: #51A2FF;
    box-shadow: 0px 0px 0px 2px var(--blue-100, #DBEAFE);
}

#searchInput::placeholder {
    color: #9ca3af;
}

.menu-header {
    padding: 12px 16px;
    font-weight: 600;
    font-size: 14px;
    color: #374151;
    /* border-bottom: 1px solid #e5e7eb; */
    /* letter-spacing: 0.025em; */
    display: flex;
    align-items: center;
    gap: 8px;
}

.ui-components-header::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    display: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z' /%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
}

.component-list {
    max-height: none;
    overflow-y: visible;
    padding: 8px 16px;
}

.component-item {
    padding: 10px 12px;
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 4px;
    margin-bottom: 4px;
}

.component-item:nth-child(odd) {
    background-color: #f9fafb;
}

.component-item:hover {
    background-color: #f3f4f6;
}

.component-item.selected {
    background-color: #e6f7f2;
    color: #10b981;
}

/* Checkbox styling */
.component-item {
    display: flex;
    align-items: center;
}

.component-item input[type="checkbox"] {
    margin-right: 10px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: #10b981;
}

.component-item input[type="checkbox"]:checked+label {
    color: #10b981;
    font-weight: 500;
}

.component-item input[type="checkbox"]:checked {
    background-color: #10b981;
}

.component-item input[type="checkbox"]:checked~.component-item {
    background-color: #e6f7f2;
}

.component-item label {
    flex: 1;
    cursor: pointer;
    font-size: 14px;
    color: #4b5563;
}

.menu-header--iconandtext {
    display: flex;
    gap: 8px;
    align-items: center;
    flex: 1;

    span {
        color: var(--slate-500, #62748E);
        font-family: Figtree;
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: 100%;
        /* 15px */
    }
}

/* Save button styling */
.save-btn {
    display: flex;
    padding: 8px 10px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border: none;
    border-radius: 6px;
    background: var(--green-700, #008236);

    color: white;
    font-size: 15px;
    font-weight: 500;
    line-height: 100%;

    transition: all ease-in 200ms;

    cursor: pointer;
}


.save-btn:hover {
    background: var(--green-800, #016630);
}


/* Help section styling */
.help-section {
    display: flex;
    padding: 12px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;

    border-radius: 8px;
    border: 1px dashed var(--orange-200, #FFD6A7);
    background: var(--orange-50, #FFF7ED);

    margin: 12px;
}

.help-section p {
    margin: 0;
    align-self: stretch;
    color: var(--slate-600, #45556C);
    font-family: Figtree;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 130%;
    /* 15.6px */
}

.help-buttons {
    display: flex;
    gap: 8px;
}

.help-buttons .unsure-btn {
    display: flex;
    padding: 8px 10px;
    justify-content: center;
    align-items: center;
    gap: 8px;



    border-radius: 6px;
    border: 0.5px solid var(--orange-700, #CA3500);
    background: var(--orange-100, #FFEDD4);

    transition: all ease-in 200ms;

    cursor: pointer;

    span {
        color: var(--orange-700, #CA3500);
        font-family: Figtree;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 100%;
        /* 14px */
    }
}

.help-buttons .unsure-btn:hover {
    background: var(--orange-200, #FFD6A7);
}

.help-buttons .not-ui-btn {
    display: flex;
    padding: 8px 10px;
    justify-content: center;
    align-items: center;
    gap: 8px;

    border-radius: 6px;
    border: 0.5px solid var(--slate-600, #45556C);
    background: var(--slate-200, #E2E8F0);

    transition: all ease-in 200ms;

    cursor: pointer;

    span {
        color: var(--slate-600, #45556C);
        font-family: Figtree;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 100%;
        /* 14px */
    }
}

.help-buttons .not-ui-btn:hover {
    background: var(--slate-300, #CAD5E2);
}

/* Annotated elements will use a fixed red color */

/* Popup styles */
#toggleListBtn {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999;
    padding: 8px 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#elementListModal {
    display: none;
    position: fixed;
    top: 60px;
    right: 20px;
    background: white;
    border: 1px solid #ccc;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

#elementListModal ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#elementListModal li {
    cursor: pointer;
    padding: 4px 0;
    border-bottom: 1px solid #eee;
}

#elementListModal li:hover {
    background-color: #f0f0f0;
}

/* Multi-label popup styles */
#popupOverlay {
    position: fixed;
    top: 0;
    left: 400px;
    /* Start after the left menu */
    width: calc(100% - 800px);
    /* Leave space for both component menus */
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2999;
    display: none;
}

#multiLabelPopup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* Default center positioning, will be overridden when positioned next to box */
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    padding: 24px;
    z-index: 3000;
    display: none;
    text-align: center;
    width: 400px;
    /* Fixed width instead of min-width for better positioning */
    transition: none;
    /* No transition to avoid animation when repositioning */
    font-family: 'Figtree', sans-serif;
    /* Ensure popup is not affected by scaling */
    transform-origin: center center;
}

/* Arrow indicator styles */
.popup-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    display: none;
    /* Hidden by default, will be shown and positioned by JS */

    /* display: none; */
}

/* Arrow positions */
.arrow-left {
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    border-right-color: white;
    display: block;
}

.arrow-right {
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: white;
    display: block;
}

.arrow-top {
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: white;
    display: block;
}

.arrow-bottom {
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: white;
    display: block;
}

#multiLabelPopup p {
    margin: 0 0 24px 0;
    font-size: 16px;
    color: #374151;
    line-height: 1.5;
}

#multiLabelPopup .popup-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
}

#multiLabelPopup button {
    padding: 10px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

#yesBtn {
    background-color: #10b981;
    color: white;
}

#yesBtn:hover {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#noBtn {
    background-color: #f3f4f6;
    color: #4b5563;
    border: 1px solid #e5e7eb;
}

#noBtn:hover {
    background-color: #e5e7eb;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Accordion Component List Styles */
.accordion-component-list {
    max-height: none;
    overflow-y: auto;
    border-top: 1px solid #e5e7eb;
}

.accordion-group {
    border-bottom: 1px solid #e5e7eb;
}

.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    cursor: pointer;
    background-color: #F8FAFC;
    transition: all 0.2s;
    position: relative;
    border-bottom: 1px solid var(--slate-200, #E2E8F0);

    .flex.items-center {
        display: flex;
        align-items: center;
        gap: 4px;

        label-name {
            flex: 1;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            position: relative;
        }

    }

    svg {
        transition: all 0.1s ease-in;
    }

    .transition-transform {
        display: flex;
    }

    &.expanded {
        .transition-transform svg {
            transform: rotate(90deg);
        }
    }

}

.accordion-header:hover {
    background-color: hsl(210, 40%, 95%);
}

.accordion-header .label-name {
    color: var(--slate-600, #45556C);
    font-family: Figtree;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;

    position: relative;
}



.accordion-content {
    background-color: #ffffff;
    overflow: hidden;
}

.accordion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px 8px 32px;
    border-top: 1px solid #f3f4f6;
    transition: all 0.2s;
    cursor: pointer;
    position: relative;
}

.accordion-item:hover {
    background-color: #f3f4f6;
}


.accordion-item:active {
    background-color: #e5e7eb;
    transform: translateY(1px);
}

.accordion-item.highlighted-item {
    background-color: #e6f7f2;
    border-left: 3px solid #10b981;
}

.accordion-item.accepted-item {
    background-color: #f0fdf4;
    border-left: 3px solid #10b981;
}

.accept-btn.accepted {
    opacity: 0.5;
    pointer-events: none;
    cursor: default;
}

.accept-all-btn.accepted {
    opacity: 0.5;
    pointer-events: none;
    cursor: default;
}

.accordion-item .item-name {
    font-size: 14px;
    color: #4b5563;

    flex: 1;
}

.accept-all-btn {
    padding: 4px;
    background-color: transparent;
    /* color: white; */
    border: none;
    border-radius: 4px;
    /* font-size: 12px; */
    cursor: pointer;
    transition: all 0.2s ease-in;
    /* height: 20px; */
    /* width: 20px; */
    display: flex;
    align-items: center;
    justify-content: center;

    display: flex;
    align-content: center;
    justify-content: center;
}

.accept-all-btn:hover {
    background-color: hsl(161, 94%, 93%);
}

.accept-btn {
    padding: 4px;
    background-color: transparent;
    /* color: white; */
    border: none;
    border-radius: 4px;
    /* font-size: 12px; */
    cursor: pointer;
    transition: all 0.2s ease-in;

    display: flex;
    align-content: center;
    justify-content: center;
}

.accept-btn:hover {
    background-color: hsl(161, 94%, 93%);
}

.item-count {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.35);
    margin-right: 0;
    position: absolute;
    right: 0;
    transform: translateX(calc(100% + 4px)) translateY(-4px);
    font-weight: 400;
}

.highlighted-box {
    border: 3px solid #3b82f6 !important;
    background-color: rgba(59, 130, 246, 0.3) !important;
    z-index: 2000 !important;
}

.accepted-box {
    border: 3px solid #10b981 !important;
    background-color: rgba(16, 185, 129, 0.2) !important;
    z-index: 2000 !important;
}

/* Animation for the highlighted box when panning */
@keyframes pulse-highlight {
    0% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }

    50% {
        box-shadow: 0 0 0 15px rgba(59, 130, 246, 0.5);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

@keyframes zoom-pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.panning-to-box {
    animation: pulse-highlight 1.5s ease-out, zoom-pulse 1.5s ease-out;
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.7);
    z-index: 2100 !important;
}

.clickable-box {
    cursor: pointer !important;
    /* position: relative; */
}

.clickable-box::before {
    content: "Click to highlight";
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s;
    pointer-events: none;
    z-index: 3000;
    display: none;
}

.clickable-box:hover::before {
    opacity: 1;
}

.clickable-box::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" stroke="black" stroke-width="1"><path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.8;
    pointer-events: none;
    display: none;
}

.highlighted-item {
    background-color: #e6f7ff !important;
    border-left: 3px solid #3b82f6 !important;
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5) !important;
    position: relative;
    z-index: 10;
}

.box:not(.clickable-box) {
    /* display: none !important; */
}