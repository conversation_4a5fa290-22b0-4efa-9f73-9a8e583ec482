import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  server: {
    host: true, // This enables network access
    port: 5173,
    open: true,
    cors: true // Enable CORS for all requests
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  }
})
