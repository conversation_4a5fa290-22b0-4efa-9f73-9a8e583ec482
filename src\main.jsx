import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import { API_BASE_URL, SOCKET_DOMAIN, TASKS_API_URL } from './config/api.js'

// Make environment variables available globally for iframe communication
window.ENV_API_DOMAIN = API_BASE_URL;
window.ENV_SOCKET_DOMAIN = SOCKET_DOMAIN;
window.ENV_TASKS_API_DOMAIN = TASKS_API_URL;

console.log('Environment variables loaded in main app:', {
  API_DOMAIN: window.ENV_API_DOMAIN,
  SOCKET_DOMAIN: window.ENV_SOCKET_DOMAIN,
  TASKS_API_DOMAIN: window.ENV_TASKS_API_DOMAIN
});

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
